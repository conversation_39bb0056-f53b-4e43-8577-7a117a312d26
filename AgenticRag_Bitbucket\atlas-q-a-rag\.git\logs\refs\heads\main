0000000000000000000000000000000000000000 a12ad7b224c1b0ffd501d9c0fbd76bfb64984d65 berkincetin <<EMAIL>> 1751526950 +0300	clone: from https://bitbucket.org/netaxtech-team/atlas-q-a-rag.git
a12ad7b224c1b0ffd501d9c0fbd76bfb64984d65 da9b53d68fb00e0c65cab8007e149b38ffa181bd berkincetin <<EMAIL>> 1751612376 +0300	commit: Added detailed logs
da9b53d68fb00e0c65cab8007e149b38ffa181bd a5d8db20b79c466f9af802adbf42aa42fea4e748 berkincetin <<EMAIL>> 1751613535 +0300	commit: requirements.backend-only restored
a5d8db20b79c466f9af802adbf42aa42fea4e748 3e9f1fe35ba512197ab3859be8af12739f3846ed berkincetin <<EMAIL>> 1751622629 +0300	commit: sql connection string changed from 10.1.1.83 to 10.1.1.144
3e9f1fe35ba512197ab3859be8af12739f3846ed 9a7b246098ec0962142e7e50f2b7d4306b99117b berkincetin <<EMAIL>> 1751622636 +0300	pull --tags origin main: Merge made by the 'ort' strategy.
9a7b246098ec0962142e7e50f2b7d4306b99117b 99be61fe91cfd767f61f830d03418cbb9774c7a1 berkincetin <<EMAIL>> 1751627100 +0300	commit: web search depth changed
99be61fe91cfd767f61f830d03418cbb9774c7a1 6ab5930d00e6c9be0f839cced346f81dc1f3276c berkincetin <<EMAIL>> 1751891813 +0300	commit: sql query tool fixed
6ab5930d00e6c9be0f839cced346f81dc1f3276c 80d528b5ebe6dc4aad0303d02dc25380283ba7bb berkincetin <<EMAIL>> 1751892218 +0300	commit: config files update
80d528b5ebe6dc4aad0303d02dc25380283ba7bb 21d6e051e2e1f311a01218ba5798394d7395a5d8 berkincetin <<EMAIL>> 1751894650 +0300	commit: document processing update
21d6e051e2e1f311a01218ba5798394d7395a5d8 23bdad107ca4631b1be09b358a8a66c39611592b berkincetin <<EMAIL>> 1751972159 +0300	commit: tool selection fixed
23bdad107ca4631b1be09b358a8a66c39611592b 72cfd0b6b34e10bc8d102b3ff13d68fe96159f25 berkincetin <<EMAIL>> 1751982888 +0300	commit: langchain_chroma library added
72cfd0b6b34e10bc8d102b3ff13d68fe96159f25 869392cfd3882d7e670d3525cd15b76080019079 berkincetin <<EMAIL>> 1751983064 +0300	commit: dockerfile-requirements file changed
869392cfd3882d7e670d3525cd15b76080019079 26da7e75457be1635add69e84edb36e498927f05 berkincetin <<EMAIL>> 1752217218 +0300	commit: sql query test function added
26da7e75457be1635add69e84edb36e498927f05 64e494d199988ddb42b2daba048a559968383278 berkincetin <<EMAIL>> 1752234086 +0300	commit: sql query test function update
64e494d199988ddb42b2daba048a559968383278 4596aad971eb529c5b5d3414756a8209cbc8f724 berkincetin <<EMAIL>> 1752237708 +0300	commit: missing libidoc package added
4596aad971eb529c5b5d3414756a8209cbc8f724 32dae4b3ba5292d09ef58b07136458f7097c8f9b berkincetin <<EMAIL>> 1752238240 +0300	commit: unixodbc added
32dae4b3ba5292d09ef58b07136458f7097c8f9b 678f6304390e32e08e4e7a05550c2fd435e90ab8 berkincetin <<EMAIL>> 1752239670 +0300	commit: ms sql odbc driver update
