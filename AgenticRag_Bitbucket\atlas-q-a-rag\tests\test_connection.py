#!/usr/bin/env python
"""
Simple connection test for the VERSISDB database.
"""

import pyodbc
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def test_pyodbc_connection():
    """Test direct pyodbc connection."""

    # Connection string
    conn_str = (
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=10.1.1.114\\ATLASMAINDB;"
        "DATABASE=VERSISDB;"
        "UID=netaxtech;"
        "PWD=Net@xAdmin22!;"
        "TrustServerCertificate=yes;"
    )

    logger.info("Testing direct pyodbc connection...")
    logger.info(f"Connection string: {conn_str}")

    try:
        # Test connection
        logger.info("Attempting to connect...")
        conn = pyodbc.connect(conn_str, timeout=30)
        logger.info("✅ Connection successful!")

        # Test a simple query
        cursor = conn.cursor()
        logger.info("Testing simple query...")
        cursor.execute("SELECT 1 as test_value")
        result = cursor.fetchone()
        logger.info(f"✅ Query successful! Result: {result}")

        # Test table listing
        logger.info("Testing table listing...")
        cursor.execute(
            "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME"
        )
        tables = cursor.fetchall()
        logger.info(f"✅ Found {len(tables)} tables:")
        for table in tables:
            logger.info(f"  - {table[0]}")

        # Test a few tables that exist
        test_tables = ["Users", "Faculties", "Departments"]
        for table_name in test_tables:
            try:
                logger.info(f"Testing {table_name} table...")
                cursor.execute(f"SELECT TOP 3 * FROM {table_name}")
                rows = cursor.fetchall()
                logger.info(f"✅ Found {len(rows)} rows in {table_name} table")

                # Get column names
                columns = [column[0] for column in cursor.description]
                logger.info(f"{table_name} table columns: {', '.join(columns)}")

            except pyodbc.Error as table_error:
                logger.warning(
                    f"⚠️ Could not access {table_name} table: {str(table_error)}"
                )
                continue

        # Close connection
        conn.close()
        logger.info("✅ Connection closed successfully")

        return True

    except pyodbc.Error as e:
        logger.error(f"❌ PyODBC Error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ General Error: {str(e)}")
        return False


def main():
    """Main test function."""
    print("VERSISDB Connection Test")
    print("=======================")
    print("Testing direct pyodbc connection to VERSISDB...")
    print()

    success = test_pyodbc_connection()

    if success:
        print("\n🎉 Connection test completed successfully!")
        print("The database is accessible and the SQL query tool should work.")
    else:
        print("\n❌ Connection test failed!")
        print("Please check:")
        print("1. Network connectivity to 10.1.1.114")
        print("2. SQL Server instance ATLASMAINDB is running")
        print("3. Firewall settings allow connections")
        print("4. Credentials are correct")


if __name__ == "__main__":
    main()
