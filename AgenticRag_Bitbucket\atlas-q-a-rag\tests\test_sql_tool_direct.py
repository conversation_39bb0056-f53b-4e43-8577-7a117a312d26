#!/usr/bin/env python
"""
Direct test of the SQL query tool using pyodbc directly.
"""

import asyncio
import json
import logging
import sys
import os
import yaml
import pyodbc
from pathlib import Path
from dotenv import load_dotenv

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()


# Load connection string from admin_bot.yaml
def load_admin_bot_config():
    """Load the admin bot configuration to get the connection string."""
    config_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        "configs",
        "admin_bot.yaml",
    )

    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
            return config
    except Exception as e:
        logger.error(f"Error loading admin bot config: {e}")
        return None


# Test SQL queries for VERSISDB
TEST_QUERIES = [
    # Basic SELECT queries - using SQL Server syntax with TOP instead of LIMIT
    "SELECT TOP 5 * FROM Departments",
    "SELECT TOP 5 * FROM Faculties",
    "SELECT TOP 3 * FROM AcademicTitles",
    "SELECT TOP 5 * FROM Users",
    "SELECT TOP 3 * FROM Roles",
    # Simple WHERE queries with correct data types
    "SELECT * FROM Faculties WHERE IsDeleted = 0",
    "SELECT TOP 5 * FROM Departments WHERE IsActive = 1",
    "SELECT TOP 3 * FROM AcademicTitles WHERE IsDeleted = 0",
    # COUNT queries
    "SELECT COUNT(*) as total_departments FROM Departments",
    "SELECT COUNT(*) as total_faculties FROM Faculties",
    "SELECT COUNT(*) as total_academic_titles FROM AcademicTitles",
]


def test_direct_pyodbc():
    """Test direct pyodbc queries."""

    # Load admin bot config
    admin_config = load_admin_bot_config()
    if not admin_config:
        logger.error("Could not load admin bot configuration")
        return False

    # Get SQL tool config from admin bot config
    sql_tool_config = None
    for tool in admin_config.get("tools", []):
        if tool.get("type") == "SQLQueryTool":
            sql_tool_config = tool.get("config", {})
            break

    if not sql_tool_config:
        logger.error("Could not find SQLQueryTool configuration in admin_bot.yaml")
        return False

    connection_string = sql_tool_config.get("connection_string")
    logger.info(f"Using connection string: {connection_string}")

    # Test connection first
    logger.info("\n=== Testing Database Connection ===")
    try:
        # Simple connection test
        conn = pyodbc.connect(connection_string, timeout=30)
        logger.info("✅ Database connection successful!")

        cursor = conn.cursor()
        cursor.execute("SELECT 1 as test_connection")
        result = cursor.fetchone()
        logger.info(f"✅ Simple query successful! Result: {result}")

        conn.close()

    except Exception as e:
        logger.error(f"❌ Database connection error: {str(e)}")
        return False

    # Test each direct SQL query
    success = True
    logger.info(f"\n=== Testing Direct SQL Queries ===")

    for i, query in enumerate(TEST_QUERIES):
        logger.info(f"\n--- Testing query {i+1}/{len(TEST_QUERIES)} ---")
        logger.info(f"Query: {query}")

        try:
            # Execute the query
            conn = pyodbc.connect(connection_string, timeout=30)
            cursor = conn.cursor()
            cursor.execute(query)

            # Get results
            rows = cursor.fetchmany(10)  # Limit to 10 results
            columns = [column[0] for column in cursor.description]

            logger.info(f"✅ Query successful!")
            logger.info(f"Found {len(rows)} results")
            logger.info(f"Columns: {', '.join(columns)}")

            # Print a sample of the results if any were found
            if len(rows) > 0:
                # Convert row to dict for JSON serialization
                sample_result = dict(zip(columns, rows[0]))
                logger.info(
                    f"Sample result: {json.dumps(sample_result, indent=2, default=str)}"
                )

            conn.close()

        except Exception as e:
            logger.error(f"❌ Query execution error: {str(e)}")
            success = False

    return success


def main():
    """Main test function."""
    print("Direct PyODBC SQL Query Test with VERSISDB")
    print("=========================================")
    print("This test will:")
    print("1. Load connection string from admin_bot.yaml")
    print("2. Test database connection using pyodbc directly")
    print("3. Run direct SQL queries")
    print()

    # Run the tests
    success = test_direct_pyodbc()

    if success:
        print("\n🎉 All direct SQL tests completed successfully!")
        print("The connection string and queries are working correctly.")
    else:
        print("\n❌ Some direct SQL tests failed. Check the logs for details.")


if __name__ == "__main__":
    main()
